<?php
/**
 * Plugin Name: AITO Reviews
 * Plugin URI: https://absoluteescapes.com
 * Description: Connects to AITO SOAP API to fetch review scores and totals, and displays them as a widget on the frontend using WordPress cron.
 * Version: 1.0.0
 * Author: Absolute Escapes
 * License: GPL v2 or later
 * Text Domain: aito-reviews
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('AITO_REVIEWS_VERSION', '1.0.0');
define('AITO_REVIEWS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('AITO_REVIEWS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AITO_REVIEWS_PLUGIN_FILE', __FILE__);

/**
 * Main plugin class
 */
class AITO_Reviews_Plugin {
    
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize plugin
     */
    private function init() {
        // Load required files
        $this->load_dependencies();
        
        // Initialize hooks
        add_action('init', array($this, 'init_hooks'));
        
        // Activation and deactivation hooks
        register_activation_hook(AITO_REVIEWS_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(AITO_REVIEWS_PLUGIN_FILE, array($this, 'deactivate'));
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        require_once AITO_REVIEWS_PLUGIN_DIR . 'includes/class-aito-soap-client.php';
        require_once AITO_REVIEWS_PLUGIN_DIR . 'includes/class-aito-admin.php';
        require_once AITO_REVIEWS_PLUGIN_DIR . 'includes/class-aito-cron.php';
        require_once AITO_REVIEWS_PLUGIN_DIR . 'includes/class-aito-widget.php';
        require_once AITO_REVIEWS_PLUGIN_DIR . 'includes/aito-functions.php';
    }
    
    /**
     * Initialize hooks
     */
    public function init_hooks() {
        // Initialize admin interface
        if (is_admin()) {
            new AITO_Reviews_Admin();
        }

        // Initialize cron functionality
        new AITO_Reviews_Cron();

        // Initialize widget functionality
        new AITO_Reviews_Widget();

        // Add custom cron schedules
        add_filter('cron_schedules', array($this, 'add_cron_schedules'));

        // Enqueue frontend styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_styles'));
    }
    
    /**
     * Add custom cron schedules
     */
    public function add_cron_schedules($schedules) {
        // Add 15 minutes schedule
        $schedules['fifteen_minutes'] = array(
            'interval' => 15 * 60,
            'display' => __('Every 15 Minutes', 'aito-reviews')
        );
        
        // Add 30 minutes schedule
        $schedules['thirty_minutes'] = array(
            'interval' => 30 * 60,
            'display' => __('Every 30 Minutes', 'aito-reviews')
        );
        
        // Add 2 hours schedule
        $schedules['two_hours'] = array(
            'interval' => 2 * 60 * 60,
            'display' => __('Every 2 Hours', 'aito-reviews')
        );
        
        // Add 6 hours schedule
        $schedules['six_hours'] = array(
            'interval' => 6 * 60 * 60,
            'display' => __('Every 6 Hours', 'aito-reviews')
        );
        
        return $schedules;
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check system requirements
        $requirements_check = $this->check_system_requirements();
        if (!$requirements_check['meets_requirements']) {
            deactivate_plugins(plugin_basename(AITO_REVIEWS_PLUGIN_FILE));
            wp_die(
                '<h1>' . __('Plugin Activation Error', 'aito-reviews') . '</h1>' .
                '<p>' . __('AITO Reviews plugin cannot be activated due to missing system requirements:', 'aito-reviews') . '</p>' .
                '<ul><li>' . implode('</li><li>', $requirements_check['missing']) . '</li></ul>' .
                '<p><a href="' . admin_url('plugins.php') . '">' . __('Return to Plugins', 'aito-reviews') . '</a></p>',
                __('Plugin Activation Error', 'aito-reviews'),
                array('back_link' => true)
            );
        }

        // Set default options
        $default_options = array(
            'endpoint_url' => 'https://www.aito.com/api/v1/api.asmx',
            'cron_interval' => 'hourly',
            'enabled' => false,
            'username' => '',
            'password' => '',
            'company_id' => '',
            'method' => 'getFeedbackRatingA',
            'last_update' => 0,
            'last_error' => ''
        );

        foreach ($default_options as $key => $value) {
            $option_name = 'aito_reviews_' . $key;
            if (false === get_option($option_name)) {
                add_option($option_name, $value);
            }
        }

        // Schedule cron job if enabled
        $this->schedule_cron();

        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled cron job
        $this->clear_cron();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Schedule cron job
     */
    private function schedule_cron() {
        if (!wp_next_scheduled('aito_reviews_fetch_data')) {
            $interval = get_option('aito_reviews_cron_interval', 'hourly');
            wp_schedule_event(time(), $interval, 'aito_reviews_fetch_data');
        }
    }
    
    /**
     * Clear cron job
     */
    private function clear_cron() {
        $timestamp = wp_next_scheduled('aito_reviews_fetch_data');
        if ($timestamp) {
            wp_unschedule_event($timestamp, 'aito_reviews_fetch_data');
        }
    }
    
    /**
     * Reschedule cron job with new interval
     */
    public function reschedule_cron($new_interval) {
        $this->clear_cron();

        if (get_option('aito_reviews_enabled', false)) {
            wp_schedule_event(time(), $new_interval, 'aito_reviews_fetch_data');
        }
    }

    /**
     * Enqueue frontend styles
     */
    public function enqueue_frontend_styles() {
        wp_enqueue_style(
            'aito-reviews-frontend',
            AITO_REVIEWS_PLUGIN_URL . 'assets/style.css',
            array(),
            AITO_REVIEWS_VERSION
        );
    }

    /**
     * Check system requirements
     */
    private function check_system_requirements() {
        $requirements = array(
            'php_version' => '7.0',
            'extensions' => array(
                'soap' => __('PHP SOAP Extension', 'aito-reviews'),
                'openssl' => __('PHP OpenSSL Extension', 'aito-reviews'),
                'curl' => __('PHP cURL Extension', 'aito-reviews'),
                'libxml' => __('PHP LibXML Extension', 'aito-reviews')
            )
        );

        $missing = array();
        $meets_requirements = true;

        // Check PHP version
        if (version_compare(PHP_VERSION, $requirements['php_version'], '<')) {
            $missing[] = sprintf(
                __('PHP %s or higher (current version: %s)', 'aito-reviews'),
                $requirements['php_version'],
                PHP_VERSION
            );
            $meets_requirements = false;
        }

        // Check required extensions
        foreach ($requirements['extensions'] as $extension => $name) {
            if (!extension_loaded($extension)) {
                $missing[] = $name;
                $meets_requirements = false;
            }
        }

        return array(
            'meets_requirements' => $meets_requirements,
            'missing' => $missing,
            'requirements' => $requirements
        );
    }

    /**
     * Get system information
     */
    public function get_system_info() {
        return array(
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'soap_enabled' => extension_loaded('soap'),
            'openssl_enabled' => extension_loaded('openssl'),
            'curl_enabled' => extension_loaded('curl'),
            'libxml_enabled' => extension_loaded('libxml'),
            'dom_enabled' => extension_loaded('dom'),
            'simplexml_enabled' => extension_loaded('simplexml'),
            'allow_url_fopen' => ini_get('allow_url_fopen') ? true : false,
            'max_execution_time' => ini_get('max_execution_time'),
            'memory_limit' => ini_get('memory_limit')
        );
    }
}

// Initialize plugin
AITO_Reviews_Plugin::get_instance();
