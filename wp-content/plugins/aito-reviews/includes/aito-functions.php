<?php
/**
 * AITO Reviews Public Functions
 * 
 * Public API functions for accessing review data
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get the current review score
 * 
 * @return float The review score
 */
function aito_get_review_score() {
    return floatval(get_option('aito_reviews_score', 0));
}

/**
 * Get the total number of reviews
 * 
 * @return int The total number of reviews
 */
function aito_get_review_total() {
    return intval(get_option('aito_reviews_total', 0));
}

/**
 * Get all review data
 * 
 * @return array Array containing score, total, and metadata
 */
function aito_get_review_data() {
    $data = get_option('aito_reviews_data', array());
    
    // Ensure we have default values
    $default_data = array(
        'score' => 0,
        'total' => 0,
        'last_updated' => 0,
        'raw_response' => null
    );
    
    return wp_parse_args($data, $default_data);
}

/**
 * Get the last update timestamp
 * 
 * @return int Unix timestamp of last update
 */
function aito_get_last_update() {
    return intval(get_option('aito_reviews_last_update', 0));
}

/**
 * Get formatted last update time
 * 
 * @param string $format Date format (default: WordPress date format)
 * @return string Formatted date string
 */
function aito_get_last_update_formatted($format = null) {
    $timestamp = aito_get_last_update();
    
    if (!$timestamp) {
        return __('Never', 'aito-reviews');
    }
    
    if (!$format) {
        $format = get_option('date_format') . ' ' . get_option('time_format');
    }
    
    return date($format, $timestamp);
}

/**
 * Get human-readable time since last update
 * 
 * @return string Human-readable time difference
 */
function aito_get_time_since_update() {
    $timestamp = aito_get_last_update();
    
    if (!$timestamp) {
        return __('Never updated', 'aito-reviews');
    }
    
    return sprintf(
        __('%s ago', 'aito-reviews'),
        human_time_diff($timestamp, current_time('timestamp'))
    );
}

/**
 * Check if review data is available
 * 
 * @return bool True if data is available
 */
function aito_has_review_data() {
    $score = aito_get_review_score();
    $total = aito_get_review_total();
    
    return ($score > 0 || $total > 0);
}

/**
 * Get review score as formatted string
 *
 * @param int $decimals Number of decimal places
 * @return string Formatted score
 */
function aito_get_review_score_formatted($decimals = 1) {
    $score = aito_get_review_score();
    return number_format($score, $decimals);
}

/**
 * Display AITO widget with reviews and rating
 *
 * @param bool $echo Whether to echo or return the output
 * @return string|void Widget HTML output
 */
function aito_display_widget($echo = true) {
    // Check if data is available
    if (!aito_has_review_data()) {
        if ($echo) {
            return;
        } else {
            return '';
        }
    }

    $score = aito_get_review_score();
    $total = aito_get_review_total();

    // Get plugin URL for SVG path
    $plugin_url = defined('AITO_REVIEWS_PLUGIN_URL') ? AITO_REVIEWS_PLUGIN_URL : plugin_dir_url(dirname(__FILE__) . '/../');
    $svg_url = $plugin_url . 'assets/images/aito-tick.svg';

    // Build widget HTML
    $output = '<div class="aito-widget">';
    $output .= '<span class="aito-total"><strong>' . esc_html(number_format($total)) . '</strong> AITO Reviews</span>';
    $output .= '<span class="aito-score">' . esc_html($score) . '%</span>';
    $output .= '<span class="aito-ticks">';
    for ($i = 0; $i < 5; $i++) {
        $output .= '<span class="aito-tick"></span>';
    }
    $output .= '</span>';
    $output .= '</div>';

    // Add inline CSS
    $output .= '<style>
        .review-bar__review {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        .aito-widget {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            font-family: upgrade, sans-serif;
            font-size: 21px;
            color: #343434;
            font-weight: 300;
        }
        .aito-total {
            color: #343434;
            font-weight: 300;
        }
        .aito-total strong {
            font-weight: 500;
        }
        .aito-score {
            color: #343434;
            font-weight: 500;
        }
        .aito-ticks {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .aito-tick {
            width: 23px;
            height: 23px;
            background-image: url("' . esc_url($svg_url) . '");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    </style>';

    if ($echo) {
        echo $output;
    } else {
        return $output;
    }
}

/**
 * Get review total as formatted string
 * 
 * @return string Formatted total with thousands separator
 */
function aito_get_review_total_formatted() {
    $total = aito_get_review_total();
    return number_format($total);
}

/**
 * Display review score
 * 
 * @param int $decimals Number of decimal places
 * @param bool $echo Whether to echo or return
 * @return string|void
 */
function aito_review_score($decimals = 1, $echo = true) {
    $score = aito_get_review_score_formatted($decimals);
    
    if ($echo) {
        echo esc_html($score);
    } else {
        return $score;
    }
}

/**
 * Display review total
 * 
 * @param bool $echo Whether to echo or return
 * @return string|void
 */
function aito_review_total($echo = true) {
    $total = aito_get_review_total_formatted();
    
    if ($echo) {
        echo esc_html($total);
    } else {
        return $total;
    }
}

/**
 * Display complete review summary
 * 
 * @param array $args Display arguments
 * @param bool $echo Whether to echo or return
 * @return string|void
 */
function aito_review_summary($args = array(), $echo = true) {
    $defaults = array(
        'show_score' => true,
        'show_total' => true,
        'show_last_update' => false,
        'score_decimals' => 1,
        'wrapper_class' => 'aito-review-summary',
        'score_class' => 'aito-review-score',
        'total_class' => 'aito-review-total',
        'update_class' => 'aito-review-update',
        'template' => 'default'
    );
    
    $args = wp_parse_args($args, $defaults);
    
    if (!aito_has_review_data()) {
        $output = '<div class="' . esc_attr($args['wrapper_class']) . ' no-data">';
        $output .= __('No review data available', 'aito-reviews');
        $output .= '</div>';
    } else {
        $score = aito_get_review_score_formatted($args['score_decimals']);
        $total = aito_get_review_total_formatted();
        
        $output = '<div class="' . esc_attr($args['wrapper_class']) . '">';
        
        if ($args['template'] === 'stars') {
            // Star rating template
            $output .= aito_get_star_rating($args);
        } else {
            // Default template
            if ($args['show_score']) {
                $output .= '<span class="' . esc_attr($args['score_class']) . '">' . esc_html($score) . '</span>';
            }
            
            if ($args['show_total']) {
                $output .= '<span class="' . esc_attr($args['total_class']) . '">';
                $output .= sprintf(_n('%s review', '%s reviews', aito_get_review_total(), 'aito-reviews'), esc_html($total));
                $output .= '</span>';
            }
        }
        
        if ($args['show_last_update']) {
            $output .= '<span class="' . esc_attr($args['update_class']) . '">';
            $output .= sprintf(__('Updated %s', 'aito-reviews'), aito_get_time_since_update());
            $output .= '</span>';
        }
        
        $output .= '</div>';
    }
    
    if ($echo) {
        echo $output;
    } else {
        return $output;
    }
}

/**
 * Get star rating HTML
 * 
 * @param array $args Star rating arguments
 * @return string Star rating HTML
 */
function aito_get_star_rating($args = array()) {
    $defaults = array(
        'max_stars' => 5,
        'star_class' => 'aito-star',
        'filled_class' => 'filled',
        'empty_class' => 'empty',
        'half_class' => 'half'
    );
    
    $args = wp_parse_args($args, $defaults);
    $score = aito_get_review_score();
    $max_stars = intval($args['max_stars']);
    
    // Calculate rating out of max stars
    $rating = ($score / 10) * $max_stars; // Assuming score is out of 10
    
    $output = '<div class="aito-star-rating">';
    
    for ($i = 1; $i <= $max_stars; $i++) {
        $class = $args['star_class'];
        
        if ($i <= floor($rating)) {
            $class .= ' ' . $args['filled_class'];
        } elseif ($i <= ceil($rating)) {
            $class .= ' ' . $args['half_class'];
        } else {
            $class .= ' ' . $args['empty_class'];
        }
        
        $output .= '<span class="' . esc_attr($class) . '">★</span>';
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Widget display functions (for backward compatibility with existing theme)
 */

/**
 * Get AITO widget instance
 */
function aito_get_widget_instance() {
    static $widget_instance = null;
    if ($widget_instance === null) {
        $widget_instance = new AITO_Reviews_Widget();
    }
    return $widget_instance;
}

/**
 * Output AITO widget HTML
 */
function aito_output_widget() {
    $widget = aito_get_widget_instance();
    return $widget->output_widget();
}

/**
 * Output AITO widget CSS
 */
function aito_output_css() {
    $widget = aito_get_widget_instance();
    return $widget->output_css();
}

/**
 * Output AITO widget JavaScript
 */
function aito_output_js() {
    $widget = aito_get_widget_instance();
    return $widget->output_js();
}

/**
 * Get plugin status information
 * 
 * @return array Status information
 */
function aito_get_plugin_status() {
    $cron = new AITO_Reviews_Cron();
    $cron_status = $cron->get_cron_status();
    
    return array(
        'enabled' => get_option('aito_reviews_enabled', false),
        'has_data' => aito_has_review_data(),
        'last_update' => aito_get_last_update(),
        'last_error' => get_option('aito_reviews_last_error', ''),
        'cron_status' => $cron_status
    );
}

/**
 * Shortcode for displaying review data
 * 
 * @param array $atts Shortcode attributes
 * @return string Shortcode output
 */
function aito_reviews_shortcode($atts) {
    $atts = shortcode_atts(array(
        'show' => 'summary', // summary, score, total, stars
        'decimals' => 1,
        'class' => '',
        'template' => 'default'
    ), $atts, 'aito_reviews');

    // Sanitize inputs
    $show = sanitize_text_field($atts['show']);
    $decimals = absint($atts['decimals']);
    $class = sanitize_html_class($atts['class']);
    $template = sanitize_text_field($atts['template']);

    // Validate show parameter
    $allowed_show = array('summary', 'score', 'total', 'stars');
    if (!in_array($show, $allowed_show)) {
        $show = 'summary';
    }

    // Validate template parameter
    $allowed_templates = array('default', 'stars');
    if (!in_array($template, $allowed_templates)) {
        $template = 'default';
    }

    // Ensure decimals is reasonable
    $decimals = min(max($decimals, 0), 5);

    $class = !empty($class) ? $class : 'aito-reviews-shortcode';

    switch ($show) {
        case 'score':
            return '<span class="' . esc_attr($class) . '">' . aito_get_review_score_formatted($decimals) . '</span>';

        case 'total':
            return '<span class="' . esc_attr($class) . '">' . aito_get_review_total_formatted() . '</span>';

        case 'stars':
            $args = array(
                'wrapper_class' => $class,
                'template' => 'stars'
            );
            return aito_review_summary($args, false);

        case 'summary':
        default:
            $args = array(
                'wrapper_class' => $class,
                'score_decimals' => $decimals,
                'template' => $template
            );
            return aito_review_summary($args, false);
    }
}

// Register shortcode
add_shortcode('aito_reviews', 'aito_reviews_shortcode');
