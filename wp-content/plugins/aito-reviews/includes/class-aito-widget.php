<?php
/**
 * AITO Reviews Widget Class
 * 
 * Handles widget display functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_Reviews_Widget {

    /**
     * Constructor
     */
    public function __construct() {
        // Widget functionality is now purely display-based
        // No admin menu needed since we don't fetch external files
    }
    
    /**
     * Output widget HTML
     */
    public function output_widget() {
        // Use the PHP-based AITO Reviews plugin data if available
        if (function_exists('aito_has_review_data') && aito_has_review_data()) {
            $score = aito_get_review_score();
            $total = aito_get_review_total();
            
            // Format the output to match the expected design
            $output = '<div class="aito-widget">';
            $output .= '<span class="aito-total"><strong>' . esc_html(number_format($total)) . '</strong> AITO Reviews</span>';
            $output .= '<span class="aito-score">' . esc_html($score) . '%</span>';
            $output .= '<span class="aito-ticks">';
            for ($i = 0; $i < 5; $i++) {
                $output .= '<span class="aito-tick"></span>';
            }
            $output .= '</span>';
            $output .= '</div>';
            
            return $output;
        }
        
        // Fallback to old JS widget if new plugin data not available
        return '<script>var buttonTemplate = "21";</script><div id="aito-widget"></div>';
    }
    
    /**
     * Output widget CSS
     */
    public function output_css() {
        // Always use PHP widget with inline CSS (no fallback needed)
        $plugin_url = defined('AITO_REVIEWS_PLUGIN_URL') ? AITO_REVIEWS_PLUGIN_URL : plugin_dir_url(dirname(__FILE__) . '/../');

        return '<style>
            .aito-widget {
                display: inline-flex;
                align-items: center;
                gap: 10px;
                font-family: upgrade, sans-serif;
                font-size: 21px;
                color: #343434;
                font-weight: 400;
            }
            .aito-total {
                color: #343434;
                font-weight: 400;
            }
            .aito-total strong {
                font-weight: 700;
            }
            .aito-score {
                color: #343434;
                font-weight: 700;
            }
            .aito-ticks {
                display: inline-flex;
                align-items: center;
                gap: 5px;
            }
            .aito-tick {
                width: 23px;
                height: 23px;
                background-image: url("' . esc_url($plugin_url . 'assets/images/aito-tick.svg') . '");
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }
        </style>';
    }

    /**
     * Output widget JavaScript
     */
    public function output_js() {
        // PHP widget requires no JavaScript
        return false;
    }
}
