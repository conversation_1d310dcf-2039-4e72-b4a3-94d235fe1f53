<?php
/**
 * AITO Reviews Admin Class
 * 
 * Handles admin interface and settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class AITO_Reviews_Admin {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'init_settings'));
        add_action('wp_ajax_aito_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_aito_fetch_now', array($this, 'ajax_fetch_now'));
        add_action('wp_ajax_aito_clear_debug', array($this, 'ajax_clear_debug'));
        add_action('wp_ajax_aito_test_auth', array($this, 'ajax_test_auth'));
        add_action('wp_ajax_aito_clear_session', array($this, 'ajax_clear_session'));
        add_action('wp_ajax_aito_view_debug_file', array($this, 'ajax_view_debug_file'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Add settings link to plugin page
        add_filter('plugin_action_links_' . plugin_basename(AITO_REVIEWS_PLUGIN_FILE), array($this, 'add_plugin_action_links'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_options_page(
            __('AITO Reviews Settings', 'aito-reviews'),
            __('AITO Reviews', 'aito-reviews'),
            'manage_options',
            'aito-reviews',
            array($this, 'admin_page')
        );
    }

    /**
     * Add settings link to plugin action links
     */
    public function add_plugin_action_links($links) {
        $settings_link = '<a href="' . esc_url(admin_url('options-general.php?page=aito-reviews')) . '">' . __('Settings', 'aito-reviews') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Initialize settings
     */
    public function init_settings() {
        // Register settings with sanitization callbacks
        register_setting('aito_reviews_settings', 'aito_reviews_endpoint_url', array(
            'sanitize_callback' => array($this, 'sanitize_endpoint_url')
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_username', array(
            'sanitize_callback' => 'sanitize_text_field'
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_password', array(
            'sanitize_callback' => 'sanitize_text_field'
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_company_id', array(
            'sanitize_callback' => 'sanitize_text_field'
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_method', array(
            'sanitize_callback' => array($this, 'sanitize_method')
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_cron_interval', array(
            'sanitize_callback' => array($this, 'sanitize_cron_interval')
        ));
        register_setting('aito_reviews_settings', 'aito_reviews_enabled', array(
            'sanitize_callback' => array($this, 'sanitize_boolean')
        ));
        
        // Add settings sections
        add_settings_section(
            'aito_reviews_api_section',
            __('API Configuration', 'aito-reviews'),
            array($this, 'api_section_callback'),
            'aito_reviews_settings'
        );
        
        add_settings_section(
            'aito_reviews_cron_section',
            __('Cron Settings', 'aito-reviews'),
            array($this, 'cron_section_callback'),
            'aito_reviews_settings'
        );
        
        add_settings_section(
            'aito_reviews_status_section',
            __('Status & Data', 'aito-reviews'),
            array($this, 'status_section_callback'),
            'aito_reviews_settings'
        );

        add_settings_section(
            'aito_reviews_system_section',
            __('System Information', 'aito-reviews'),
            array($this, 'system_section_callback'),
            'aito_reviews_settings'
        );
        
        // Add settings fields
        add_settings_field(
            'aito_reviews_endpoint_url',
            __('API Endpoint URL', 'aito-reviews'),
            array($this, 'endpoint_url_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_username',
            __('Username', 'aito-reviews'),
            array($this, 'username_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_password',
            __('Password', 'aito-reviews'),
            array($this, 'password_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_company_id',
            __('Company ID', 'aito-reviews'),
            array($this, 'company_id_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );

        add_settings_field(
            'aito_reviews_method',
            __('API Method', 'aito-reviews'),
            array($this, 'method_field'),
            'aito_reviews_settings',
            'aito_reviews_api_section'
        );
        
        add_settings_field(
            'aito_reviews_enabled',
            __('Enable Automatic Updates', 'aito-reviews'),
            array($this, 'enabled_field'),
            'aito_reviews_settings',
            'aito_reviews_cron_section'
        );
        
        add_settings_field(
            'aito_reviews_cron_interval',
            __('Update Interval', 'aito-reviews'),
            array($this, 'cron_interval_field'),
            'aito_reviews_settings',
            'aito_reviews_cron_section'
        );
    }
    
    /**
     * Admin page
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>

            <?php
            // Only show settings errors for our specific settings group
            settings_errors('aito_reviews_settings');
            ?>
            
            <form method="post" action="options.php" id="aito-settings-form">
                <?php
                settings_fields('aito_reviews_settings');
                do_settings_sections('aito_reviews_settings');
                ?>
            </form>

            <div class="aito-reviews-actions">
                <h3><?php _e('Actions', 'aito-reviews'); ?></h3>
                <p>
                    <button type="button" id="aito-test-connection" class="button button-secondary">
                        <?php _e('Test Connection', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-test-auth" class="button button-secondary">
                        <?php _e('Test Authentication', 'aito-reviews'); ?>
                    </button>
                    <button type="button" id="aito-fetch-now" class="button button-primary">
                        <?php _e('Fetch Data Now', 'aito-reviews'); ?>
                    </button>
                </p>
                <div id="aito-action-results"></div>

                <!-- Debug Controls -->
                <div style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 4px; border-left: 4px solid #666;">
                    <h4 style="margin-top: 0; color: #666;"><?php _e('Debug & Diagnostics', 'aito-reviews'); ?></h4>
                    <p>
                        <button type="button" id="aito-clear-debug" class="button button-secondary">
                            <?php _e('Clear Debug Log', 'aito-reviews'); ?>
                        </button>
                        <button type="button" id="aito-view-debug-file" class="button button-secondary">
                            <?php _e('View Debug File', 'aito-reviews'); ?>
                        </button>
                    </p>
                    <p class="description">
                        <?php _e('Use these tools when troubleshooting connection issues or service interruptions.', 'aito-reviews'); ?>
                    </p>
                </div>
            </div>

            <?php $this->display_debug_log(); ?>
        </div>
        <?php
    }
    
    /**
     * Section callbacks
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure your AITO API connection settings.', 'aito-reviews') . '</p>';
    }
    
    public function cron_section_callback() {
        echo '<p>' . __('Configure automatic data fetching schedule.', 'aito-reviews') . '</p>';

        // Get cron status information
        $cron = new AITO_Reviews_Cron();
        $cron_status = $cron->get_cron_status();
        $next_run = $cron->get_next_run();

        // Check if WordPress cron is disabled
        $wp_cron_disabled = defined('DISABLE_WP_CRON') && DISABLE_WP_CRON;

        echo '<div class="aito-cron-status" style="background: #f9f9f9; padding: 15px; border-left: 4px solid #0073aa; margin: 15px 0;">';
        echo '<h4 style="margin-top: 0;">' . __('Cron Status', 'aito-reviews') . '</h4>';

        // WordPress Cron Status
        echo '<p><strong>' . __('WordPress Cron:', 'aito-reviews') . '</strong> ';
        if ($wp_cron_disabled) {
            echo '<span style="color: #d63638;">✗ ' . __('DISABLED', 'aito-reviews') . '</span>';
            echo '<br><em style="color: #d63638;">' . __('WordPress cron is disabled via DISABLE_WP_CRON constant. Automatic updates will not work.', 'aito-reviews') . '</em>';
        } else {
            echo '<span style="color: #00a32a;">✓ ' . __('ACTIVE', 'aito-reviews') . '</span>';
        }
        echo '</p>';

        // Plugin Cron Schedule Status
        echo '<p><strong>' . __('Plugin Schedule:', 'aito-reviews') . '</strong> ';
        if ($cron_status['is_scheduled']) {
            echo '<span style="color: #00a32a;">✓ ' . __('SCHEDULED', 'aito-reviews') . '</span>';
        } else {
            echo '<span style="color: #d63638;">✗ ' . __('NOT SCHEDULED', 'aito-reviews') . '</span>';
            echo '<br><em style="color: #d63638;">' . __('Plugin is not scheduled to run automatically.', 'aito-reviews') . '</em>';
        }
        echo '</p>';

        // Current Interval
        echo '<p><strong>' . __('Current Interval:', 'aito-reviews') . '</strong> ';
        $intervals = array(
            'fifteen_minutes' => __('Every 15 Minutes', 'aito-reviews'),
            'thirty_minutes' => __('Every 30 Minutes', 'aito-reviews'),
            'hourly' => __('Hourly', 'aito-reviews'),
            'two_hours' => __('Every 2 Hours', 'aito-reviews'),
            'six_hours' => __('Every 6 Hours', 'aito-reviews'),
            'twicedaily' => __('Twice Daily', 'aito-reviews'),
            'daily' => __('Daily', 'aito-reviews')
        );
        $current_interval = $cron_status['interval'];
        echo esc_html(isset($intervals[$current_interval]) ? $intervals[$current_interval] : $current_interval);
        echo '</p>';

        // Current Time for reference (using UTC to match cron scheduling)
        echo '<p><strong>' . __('Current Time:', 'aito-reviews') . '</strong> ';
        echo esc_html(date('Y-m-d H:i:s', time()));
        echo ' <em>(UTC)</em></p>';

        // Last Cron Run
        $last_update = get_option('aito_reviews_last_update', 0);
        echo '<p><strong>' . __('Last Run:', 'aito-reviews') . '</strong> ';
        if ($last_update) {
            $time_ago = human_time_diff($last_update, time()); // Use UTC time consistently
            echo esc_html(date('Y-m-d H:i:s', $last_update)) . ' <em>(' . sprintf(__('%s ago', 'aito-reviews'), $time_ago) . ')</em>';
        } else {
            echo '<span style="color: #d63638;">' . __('Never', 'aito-reviews') . '</span>';
        }
        echo '</p>';

        // Next Run Time (only show if scheduled)
        if ($cron_status['is_scheduled'] && $next_run) {
            echo '<p><strong>' . __('Next Run:', 'aito-reviews') . '</strong> ';
            echo esc_html($next_run['formatted']) . ' <em>(' . sprintf(__('in %s', 'aito-reviews'), esc_html($next_run['human'])) . ')</em>';
            echo '</p>';
        }

        echo '</div>';
    }
    
    public function status_section_callback() {
        $last_update = get_option('aito_reviews_last_update', 0);
        $last_error = get_option('aito_reviews_last_error', '');
        $score = get_option('aito_reviews_score', 0);
        $total = get_option('aito_reviews_total', 0);

        // Get session status
        $soap_client = new AITO_SOAP_Client();
        $session_status = $soap_client->get_session_status();

        echo '<div class="aito-status-info">';
        echo '<h4>' . __('Current Data', 'aito-reviews') . '</h4>';
        echo '<p><strong>' . __('Score:', 'aito-reviews') . '</strong> ' . esc_html($score) . '</p>';
        echo '<p><strong>' . __('Total Reviews:', 'aito-reviews') . '</strong> ' . esc_html($total) . '</p>';

        if ($last_update) {
            echo '<p><strong>' . __('Last Updated:', 'aito-reviews') . '</strong> ' . esc_html(date('Y-m-d H:i:s', $last_update)) . '</p>';
        } else {
            echo '<p><strong>' . __('Last Updated:', 'aito-reviews') . '</strong> ' . __('Never', 'aito-reviews') . '</p>';
        }

        // Session status
        echo '<h4>' . __('Session Status', 'aito-reviews') . '</h4>';
        if ($session_status['has_session']) {
            echo '<p><strong style="color: green;">✓ ' . esc_html($session_status['message']) . '</strong></p>';
            if (isset($session_status['authenticated_at'])) {
                echo '<p><strong>' . __('Authenticated At:', 'aito-reviews') . '</strong> ' . esc_html($session_status['authenticated_at']) . '</p>';
            }
            if (isset($session_status['expires_in'])) {
                echo '<p><strong>' . __('Expires In:', 'aito-reviews') . '</strong> ' . esc_html($session_status['expires_in']) . '</p>';
            }
            if (isset($session_status['username'])) {
                echo '<p><strong>' . __('Username:', 'aito-reviews') . '</strong> ' . esc_html($session_status['username']) . '</p>';
            }
            if (isset($session_status['auth_method'])) {
                echo '<p><strong>' . __('Auth Method:', 'aito-reviews') . '</strong> ' . esc_html($session_status['auth_method']) . '</p>';
            }
            if (isset($session_status['cookie_count']) && $session_status['cookie_count'] > 0) {
                echo '<p><strong>' . __('Session Cookies:', 'aito-reviews') . '</strong> ' . esc_html($session_status['cookie_count']) . '</p>';
            }
            echo '<p><em>' . __('Next API call will use getFeedbackRating (no auth required)', 'aito-reviews') . '</em></p>';
            echo '<p><button type="button" id="aito-clear-session" class="button button-secondary" style="color: #d63638;">Clear Session</button></p>';
        } else {
            echo '<p><strong style="color: orange;">⚠ ' . esc_html($session_status['message']) . '</strong></p>';
            if (isset($session_status['expired_at'])) {
                echo '<p><strong>' . __('Expired At:', 'aito-reviews') . '</strong> ' . esc_html($session_status['expired_at']) . '</p>';
            }
            echo '<p><em>' . __('Next API call will attempt login method first, then fallback to getFeedbackRatingA', 'aito-reviews') . '</em></p>';
        }

        if (!empty($last_error)) {
            echo '<h4>' . __('Last Error', 'aito-reviews') . '</h4>';
            echo '<p><strong style="color: red;">' . __('Error Details:', 'aito-reviews') . '</strong> ';
            echo '<button type="button" class="button-link aito-toggle-error" style="color: red; text-decoration: underline; font-size: 12px; margin-left: 5px;">Show/Hide</button></p>';
            echo '<div class="aito-error-details" style="display: none; background: #fff2f2; padding: 10px; border: 1px solid #d63638; border-radius: 3px; margin: 8px 0; max-height: 200px; overflow-y: auto;">';
            echo '<pre style="margin: 0; white-space: pre-wrap; font-size: 11px; color: #d63638;">' . esc_html($last_error) . '</pre>';
            echo '</div>';
        }
        echo '</div>';
    }

    public function system_section_callback() {
        $plugin = AITO_Reviews_Plugin::get_instance();
        $system_info = $plugin->get_system_info();

        echo '<div class="aito-system-info">';
        echo '<h4>' . __('System Requirements', 'aito-reviews') . '</h4>';
        echo '<table class="widefat">';
        echo '<thead><tr><th>' . __('Requirement', 'aito-reviews') . '</th><th>' . __('Status', 'aito-reviews') . '</th></tr></thead>';
        echo '<tbody>';

        // PHP Version
        $php_ok = version_compare($system_info['php_version'], '7.0', '>=');
        echo '<tr>';
        echo '<td>PHP 7.0+</td>';
        echo '<td><span class="' . ($php_ok ? 'aito-success' : 'aito-error') . '">';
        echo $php_ok ? '✓ ' . $system_info['php_version'] : '✗ ' . $system_info['php_version'] . ' (upgrade required)';
        echo '</span></td>';
        echo '</tr>';

        // Extensions
        $extensions = array(
            'soap_enabled' => 'SOAP Extension',
            'openssl_enabled' => 'OpenSSL Extension',
            'curl_enabled' => 'cURL Extension',
            'libxml_enabled' => 'LibXML Extension'
        );

        foreach ($extensions as $key => $name) {
            echo '<tr>';
            echo '<td>' . esc_html($name) . '</td>';
            echo '<td><span class="' . ($system_info[$key] ? 'aito-success' : 'aito-error') . '">';
            echo $system_info[$key] ? '✓ Enabled' : '✗ Not Available';
            echo '</span></td>';
            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';

        if (!$system_info['soap_enabled']) {
            echo '<div class="notice notice-error inline">';
            echo '<p><strong>' . __('SOAP Extension Required:', 'aito-reviews') . '</strong> ';
            echo __('The PHP SOAP extension is required for this plugin to work. Please contact your hosting provider to enable it.', 'aito-reviews');
            echo '</p></div>';
        }

        echo '</div>';
    }

    /**
     * Sanitization callbacks
     */
    public function sanitize_endpoint_url($value) {
        $value = sanitize_text_field($value);
        $default_url = 'https://www.aito.com/api/v1/api.asmx';

        if (empty($value)) {
            return $default_url;
        }

        if (!filter_var($value, FILTER_VALIDATE_URL)) {
            error_log('AITO Reviews Security: Invalid URL attempted - ' . $value . ' by user ' . get_current_user_id());
            add_settings_error('aito_reviews_endpoint_url', 'invalid_url', __('Please enter a valid URL for the API endpoint. Using default URL.', 'aito-reviews'));
            return $default_url; // Always return known-good default
        }

        return $value;
    }

    public function sanitize_method($value) {
        $allowed_methods = array('getFeedbackRating', 'getFeedbackRatingA', 'getFeedbackRatingB');
        if (!in_array($value, $allowed_methods)) {
            error_log('AITO Reviews Security: Invalid method attempted - ' . $value . ' by user ' . get_current_user_id());
            return 'getFeedbackRatingA'; // Default safe value
        }
        return $value;
    }

    public function sanitize_cron_interval($value) {
        $allowed_intervals = array('fifteen_minutes', 'thirty_minutes', 'hourly', 'three_hours', 'six_hours', 'twicedaily', 'daily');
        if (!in_array($value, $allowed_intervals)) {
            return 'hourly'; // Default safe value
        }
        return $value;
    }

    public function sanitize_boolean($value) {
        return (bool) $value;
    }

    /**
     * Field callbacks
     */
    public function endpoint_url_field() {
        $value = get_option('aito_reviews_endpoint_url', 'https://www.aito.com/api/v1/api.asmx');
        echo '<input type="url" name="aito_reviews_endpoint_url" value="' . esc_attr($value) . '" class="regular-text" required />';
        echo '<p class="description">' . __('The AITO SOAP API endpoint URL.', 'aito-reviews') . '</p>';
    }
    
    public function username_field() {
        $value = get_option('aito_reviews_username', '');
        echo '<input type="text" name="aito_reviews_username" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Username for API authentication (if required).', 'aito-reviews') . '</p>';
    }
    
    public function password_field() {
        $value = get_option('aito_reviews_password', '');
        echo '<input type="password" name="aito_reviews_password" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Password for API authentication (if required).', 'aito-reviews') . '</p>';
    }
    
    public function company_id_field() {
        $value = get_option('aito_reviews_company_id', '');
        echo '<input type="text" name="aito_reviews_company_id" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Company ID parameter for the API call (if required).', 'aito-reviews') . '</p>';
    }

    public function method_field() {
        // Use the proven working method
        echo '<input type="hidden" name="aito_reviews_method" value="getFeedbackRatingA" />';
        echo '<strong>getFeedbackRatingA</strong> <span style="color: #46b450;">(Proven Working Method)</span>';
        echo '<p class="description">' . __('Using the verified working authentication method for AITO API.', 'aito-reviews') . '</p>';
    }
    
    public function enabled_field() {
        $value = get_option('aito_reviews_enabled', false);
        echo '<input type="checkbox" name="aito_reviews_enabled" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label for="aito_reviews_enabled">' . __('Enable automatic data fetching via WordPress cron', 'aito-reviews') . '</label>';
    }
    
    public function cron_interval_field() {
        $value = get_option('aito_reviews_cron_interval', 'hourly');
        $intervals = array(
            'fifteen_minutes' => __('Every 15 Minutes', 'aito-reviews'),
            'thirty_minutes' => __('Every 30 Minutes', 'aito-reviews'),
            'hourly' => __('Hourly', 'aito-reviews'),
            'two_hours' => __('Every 2 Hours', 'aito-reviews'),
            'six_hours' => __('Every 6 Hours', 'aito-reviews'),
            'twicedaily' => __('Twice Daily', 'aito-reviews'),
            'daily' => __('Daily', 'aito-reviews')
        );

        echo '<select name="aito_reviews_cron_interval">';
        foreach ($intervals as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($key, $value, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('How often to fetch data from the AITO API.', 'aito-reviews') . '</p>';

        // Close the current table structure and add Save Changes button outside it
        echo '</td></tr></table>';
        echo '<input type="submit" name="submit" id="submit" class="button button-primary" value="' . esc_attr(__('Save Changes', 'aito-reviews')) . '" form="aito-settings-form" />';
        echo '<table class="form-table"><tr><td style="display:none;">';
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_aito-reviews') {
            return;
        }

        wp_enqueue_script(
            'aito-reviews-admin',
            AITO_REVIEWS_PLUGIN_URL . 'assets/admin.js',
            array('jquery'),
            AITO_REVIEWS_VERSION,
            true
        );

        wp_enqueue_style(
            'aito-reviews-admin',
            AITO_REVIEWS_PLUGIN_URL . 'assets/style.css',
            array(),
            AITO_REVIEWS_VERSION
        );

        wp_localize_script('aito-reviews-admin', 'aitoReviews', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('aito_reviews_nonce'),
            'strings' => array(
                'testing' => __('Testing connection...', 'aito-reviews'),
                'fetching' => __('Fetching data...', 'aito-reviews'),
                'success' => __('Success!', 'aito-reviews'),
                'error' => __('Error:', 'aito-reviews')
            )
        ));
    }
    
    /**
     * Check rate limiting for AJAX requests
     */
    private function check_rate_limit($action) {
        $user_id = get_current_user_id();
        $transient_key = 'aito_rate_limit_' . $action . '_' . $user_id;
        $last_request = get_transient($transient_key);

        if ($last_request !== false) {
            error_log('AITO Reviews Security: Rate limit hit for action ' . $action . ' by user ' . $user_id);
            return false;
        }

        // Set rate limit for 5 seconds
        set_transient($transient_key, time(), 5);
        return true;
    }

    /**
     * AJAX test connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        if (!$this->check_rate_limit('test_connection')) {
            wp_send_json_error(array(
                'message' => __('Please wait before making another request.', 'aito-reviews')
            ));
            return;
        }
        
        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->test_connection();
        
        wp_send_json($result);
    }
    
    /**
     * AJAX fetch data now
     */
    public function ajax_fetch_now() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        if (!$this->check_rate_limit('fetch_now')) {
            wp_send_json_error(array(
                'message' => __('Please wait before making another request.', 'aito-reviews')
            ));
            return;
        }

        // Enable debug mode for this request
        $debug_info = array();
        $debug_info['timestamp'] = current_time('mysql');
        $debug_info['settings'] = array(
            'endpoint_url' => get_option('aito_reviews_endpoint_url', ''),
            'username' => !empty(get_option('aito_reviews_username', '')) ? '[SET]' : '[EMPTY]',
            'password' => !empty(get_option('aito_reviews_password', '')) ? '[SET]' : '[EMPTY]',
            'company_id' => get_option('aito_reviews_company_id', ''),
            'enabled' => get_option('aito_reviews_enabled', false)
        );

        // Initialize SOAP client with debug
        $soap_client = new AITO_SOAP_Client();

        try {
            // Test connection first
            $debug_info['connection_test'] = $soap_client->test_connection();

            // Attempt to fetch data
            $debug_info['fetch_start'] = microtime(true);
            $data = $soap_client->get_feedback_rating();
            $debug_info['fetch_end'] = microtime(true);
            $debug_info['fetch_duration'] = round(($debug_info['fetch_end'] - $debug_info['fetch_start']) * 1000, 2) . 'ms';

            if ($data !== false) {
                // Store data
                $cron = new AITO_Reviews_Cron();
                $store_result = $cron->store_data_debug($data);

                $debug_info['success'] = true;
                $debug_info['data'] = $data;
                $debug_info['storage'] = $store_result;

                wp_send_json_success(array(
                    'message' => __('Data fetched successfully!', 'aito-reviews'),
                    'score' => $data['score'],
                    'total' => $data['total'],
                    'debug' => $debug_info
                ));
            } else {
                $debug_info['success'] = false;
                $debug_info['error'] = get_option('aito_reviews_last_error', 'Unknown error');
                $debug_info['raw_response'] = get_option('aito_reviews_raw_response', null);

                wp_send_json_error(array(
                    'message' => __('Failed to fetch data from AITO API', 'aito-reviews'),
                    'debug' => $debug_info
                ));
            }

        } catch (Exception $e) {
            $debug_info['success'] = false;
            $debug_info['exception'] = array(
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            );

            wp_send_json_error(array(
                'message' => __('Exception occurred: ', 'aito-reviews') . $e->getMessage(),
                'debug' => $debug_info
            ));
        }
    }

    /**
     * Display debug log
     */
    private function display_debug_log() {
        $soap_client = new AITO_SOAP_Client();
        $debug_log = $soap_client->get_debug_log();

        if (empty($debug_log)) {
            return;
        }

        echo '<div class="aito-debug-log-section">';
        echo '<h3>' . __('Debug Log', 'aito-reviews') . '</h3>';
        echo '<div class="aito-debug-log">';
        echo '<div class="debug-log-content" style="max-height: 300px; overflow-y: auto; background: #f9f9f9; padding: 10px; border: 1px solid #ddd;">';

        foreach (array_reverse($debug_log) as $entry) {
            echo '<div class="debug-entry">' . esc_html($entry) . '</div>';
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * AJAX clear debug log
     */
    public function ajax_clear_debug() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        // No rate limiting for clearing debug log - it's a simple file operation
        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->clear_debug_log();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Debug log cleared successfully!', 'aito-reviews')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear debug log.', 'aito-reviews')
            ));
        }
    }

    /**
     * AJAX test authentication
     */
    public function ajax_test_auth() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        if (!$this->check_rate_limit('test_auth')) {
            wp_send_json_error(array(
                'message' => __('Please wait before making another request.', 'aito-reviews')
            ));
            return;
        }

        $soap_client = new AITO_SOAP_Client();

        try {
            // Try to call a simple method to test authentication
            $method = get_option('aito_reviews_method', 'getFeedbackRating');
            $response = $soap_client->get_feedback_rating($method);

            if ($response !== false) {
                wp_send_json_success(array(
                    'message' => __('Authentication successful! Data retrieved.', 'aito-reviews'),
                    'data' => $response
                ));
            } else {
                // Get debug log for detailed error info
                $debug_log = $soap_client->get_debug_log();
                $last_error = get_option('aito_reviews_last_error', 'Unknown error');

                wp_send_json_error(array(
                    'message' => __('Authentication failed or method error.', 'aito-reviews'),
                    'error' => $last_error,
                    'debug_log' => array_slice($debug_log, -5) // Last 5 entries
                ));
            }
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Authentication test failed: ', 'aito-reviews') . $e->getMessage(),
                'exception' => array(
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }



    /**
     * AJAX clear session
     */
    public function ajax_clear_session() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        if (!$this->check_rate_limit('clear_session')) {
            wp_send_json_error(array(
                'message' => __('Please wait before making another request.', 'aito-reviews')
            ));
            return;
        }

        $soap_client = new AITO_SOAP_Client();
        $result = $soap_client->clear_session_public();

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Session cleared successfully! Next API call will re-authenticate.', 'aito-reviews')
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to clear session.', 'aito-reviews')
            ));
        }
    }



    /**
     * AJAX view debug file
     */
    public function ajax_view_debug_file() {
        check_ajax_referer('aito_reviews_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'aito-reviews'));
        }

        if (!$this->check_rate_limit('view_debug_file')) {
            wp_send_json_error(array(
                'message' => __('Please wait before making another request.', 'aito-reviews')
            ));
            return;
        }

        $upload_dir = wp_upload_dir();
        $debug_file = $upload_dir['basedir'] . '/aito-debug.log';

        if (!file_exists($debug_file)) {
            wp_send_json_success(array(
                'message' => __('Debug file does not exist yet. Run a test to create it.', 'aito-reviews'),
                'content' => '',
                'file_exists' => false
            ));
            return;
        }

        // Read the last 100 lines of the debug file
        $lines = file($debug_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $recent_lines = array_slice($lines, -100); // Last 100 lines

        wp_send_json_success(array(
            'message' => sprintf(__('Debug file contains %d total lines, showing last %d lines', 'aito-reviews'), count($lines), count($recent_lines)),
            'content' => implode("\n", $recent_lines),
            'file_exists' => true,
            'total_lines' => count($lines),
            'shown_lines' => count($recent_lines),
            'file_path' => esc_html($debug_file)
        ));
    }
}
