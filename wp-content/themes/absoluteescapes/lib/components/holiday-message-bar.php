<?php
/**
 * Holiday Message Bar Component
 * 
 * Displays an optional message bar at the top of holiday pages
 */

// Get the message bar data
$message_bar = get_field('holiday_message_bar');

// Check if message bar is enabled and has content
if (!$message_bar || !$message_bar['status'] || empty($message_bar['message'])) {
    return;
}

// Extract the data
$message = $message_bar['message'];
$link_url = !empty($message_bar['link_url']) ? $message_bar['link_url'] : '';
$link_target = !empty($message_bar['link_target']) ? $message_bar['link_target'] : '_self';
$background_color = !empty($message_bar['background_color']) ? $message_bar['background_color'] : 'teal';
$text_color = !empty($message_bar['text_color']) ? $message_bar['text_color'] : 'white';

// Build CSS classes
$css_classes = [
    'holiday-message-bar',
    'holiday-message-bar--bg-' . $background_color,
    'holiday-message-bar--text-' . $text_color
];

if ($link_url) {
    $css_classes[] = 'holiday-message-bar--clickable';
}

$css_class_string = implode(' ', $css_classes);

?>

<div class="<?php echo esc_attr($css_class_string); ?>">
    <div class="holiday-message-bar__inner">
        <div class="holiday-message-bar__container container">
            <div class="holiday-message-bar__content">
                <?php if ($link_url) : ?>
                    <a href="<?php echo esc_url($link_url); ?>" 
                       target="<?php echo esc_attr($link_target); ?>" 
                       class="holiday-message-bar__link"
                       <?php if ($link_target === '_blank') : ?>rel="noopener noreferrer"<?php endif; ?>>
                        <div class="holiday-message-bar__message">
                            <?php echo wp_kses_post(nl2br($message)); ?>
                        </div>
                        <?php if ($link_target === '_blank') : ?>
                            <div class="holiday-message-bar__icon">
                                <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                            </div>
                        <?php endif; ?>
                    </a>
                <?php else : ?>
                    <div class="holiday-message-bar__message">
                        <?php echo wp_kses_post(nl2br($message)); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
