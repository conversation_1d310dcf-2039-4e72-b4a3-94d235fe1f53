{"key": "group_holiday_message_bar", "title": "Holiday Message Bar", "fields": [{"key": "field_holiday_message_bar", "label": "Holiday Message Bar", "name": "holiday_message_bar", "aria-label": "", "type": "group", "instructions": "Optional message bar that appears at the top of holiday pages, before the primary navigation", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_holiday_message_bar_status", "label": "Show Message Bar", "name": "status", "aria-label": "", "type": "true_false", "instructions": "Enable or disable the message bar for this holiday", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Yes", "ui_off_text": "No", "ui": 1}, {"key": "field_holiday_message_bar_message", "label": "Message", "name": "message", "aria-label": "", "type": "textarea", "instructions": "The message to display in the bar", "required": 0, "conditional_logic": [[{"field": "field_holiday_message_bar_status", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "rows": 3, "placeholder": "Enter your message here...", "new_lines": ""}, {"key": "field_holiday_message_bar_link_url", "label": "Link URL", "name": "link_url", "aria-label": "", "type": "url", "instructions": "Optional: Make the message bar clickable by adding a URL", "required": 0, "conditional_logic": [[{"field": "field_holiday_message_bar_status", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "default_value": "", "placeholder": "https://example.com"}, {"key": "field_holiday_message_bar_link_target", "label": "Link Target", "name": "link_target", "aria-label": "", "type": "select", "instructions": "Choose how the link should open", "required": 0, "conditional_logic": [[{"field": "field_holiday_message_bar_status", "operator": "==", "value": "1"}, {"field": "field_holiday_message_bar_link_url", "operator": "!=", "value": ""}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"_self": "Same window", "_blank": "New window/tab"}, "default_value": "_self", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_holiday_message_bar_background_color", "label": "Background Color", "name": "background_color", "aria-label": "", "type": "select", "instructions": "Choose the background color for the message bar", "required": 0, "conditional_logic": [[{"field": "field_holiday_message_bar_status", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"teal": "<PERSON><PERSON>", "blue": "Blue", "green": "Green", "orange": "Orange", "red": "Red", "grey": "Grey"}, "default_value": "teal", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}, {"key": "field_holiday_message_bar_text_color", "label": "Text Color", "name": "text_color", "aria-label": "", "type": "select", "instructions": "Choose the text color for the message bar", "required": 0, "conditional_logic": [[{"field": "field_holiday_message_bar_status", "operator": "==", "value": "1"}]], "wrapper": {"width": "50", "class": "", "id": ""}, "choices": {"white": "White", "black": "Black", "dark": "Dark Grey"}, "default_value": "white", "return_format": "value", "multiple": 0, "allow_null": 0, "ui": 0, "ajax": 0, "placeholder": ""}]}], "location": [[{"param": "post_type", "operator": "==", "value": "holiday"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1751646080}