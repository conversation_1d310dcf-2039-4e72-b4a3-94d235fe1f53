/**
 * Holiday Message Bar Component Styles
 */

.holiday-message-bar {
    position: relative;
    z-index: 301; // Above masthead (299) and review bar
    width: 100%;
    padding: 12px 0;
    font-size: 14px;
    line-height: 1.4;
    
    @media only screen and (min-width: 768px) {
        padding: 15px 0;
        font-size: 16px;
    }

    // Background color variations
    &--bg-teal {
        background-color: $teal;
    }

    &--bg-blue {
        background-color: $bluegrey;
    }

    &--bg-green {
        background-color: $success;
    }

    &--bg-orange {
        background-color: #fd7e14;
    }

    &--bg-red {
        background-color: $fail;
    }

    &--bg-grey {
        background-color: $midlightgrey;
    }

    // Text color variations
    &--text-white {
        color: $white;
    }

    &--text-black {
        color: $black;
    }

    &--text-dark {
        color: $bluegrey;
    }

    // Clickable state
    &--clickable {
        cursor: pointer;
        transition: opacity 0.3s ease;

        &:hover {
            opacity: 0.9;
        }
    }

    &__inner {
        width: 100%;
    }

    &__container {
        max-width: 1920px;
        margin: 0 auto;
        padding: 0 15px;

        @media only screen and (min-width: 1161px) {
            padding: 0 25px;
        }
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    &__link {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: inherit;
        width: 100%;

        &:hover,
        &:focus {
            color: inherit;
            text-decoration: none;
        }
    }

    &__message {
        flex: 1;
        margin: 0;
        font-weight: 400;

        // Handle line breaks properly
        br {
            @media only screen and (max-width: 767px) {
                display: none;
            }
        }

        @media only screen and (max-width: 767px) {
            font-size: 13px;
            line-height: 1.3;
        }
    }

    &__icon {
        margin-left: 8px;
        font-size: 12px;
        opacity: 0.8;

        @media only screen and (min-width: 768px) {
            font-size: 14px;
        }
    }

    // Push down the masthead when message bar is present
    + .page-wrapper {
        .masthead {
            margin-top: 0;

            @media only screen and (min-width: 1161px) {
                // On desktop, masthead is absolutely positioned, so we need to add top offset
                &.is-fixed,
                &.is-visible,
                &.is-hidden {
                    top: 50px; // Approximate height of message bar
                }
            }
        }
    }
}

// Body class adjustments when message bar is active
body.has-holiday-message-bar {
    .masthead {
        @media only screen and (min-width: 1161px) {
            &.is-fixed,
            &.is-visible,
            &.is-hidden {
                top: 50px; // Adjust for message bar height
            }
        }
    }

    // Ensure page content accounts for both message bar and masthead
    .page-wrapper {
        @media only screen and (max-width: 1160px) {
            padding-top: 50px; // Add space for message bar on mobile
        }
    }

    // Mobile responsive adjustments
    @media only screen and (max-width: 480px) {
        padding: 10px 0;
        font-size: 12px;

        &__container {
            padding: 0 10px;
        }

        &__message {
            font-size: 12px;
            line-height: 1.2;
        }
    }
}
