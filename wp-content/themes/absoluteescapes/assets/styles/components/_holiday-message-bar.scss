/**
 * Holiday Message Bar Component Styles
 */

:root {
    --holiday-message-bar-height: 50px;
    --holiday-message-bar-height-mobile: 40px;
}

.holiday-message-bar {
    position: relative;
    z-index: 301; // Above masthead but not too high
    width: 100%;
    padding: 8px 0;
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 0;
    
    @media only screen and (min-width: 768px) {
        padding: 15px 0;
        font-size: 16px;
    }

    // Background color variations
    &--bg-teal {
        background-color: $teal;
    }

    &--bg-blue {
        background-color: $bluegrey;
    }

    &--bg-green {
        background-color: $success;
    }

    &--bg-orange {
        background-color: #fd7e14;
    }

    &--bg-red {
        background-color: $fail;
    }

    &--bg-grey {
        background-color: $midlightgrey;
    }

    // Text color variations
    &--text-white {
        color: $white;
    }

    &--text-black {
        color: $black;
    }

    &--text-dark {
        color: $bluegrey;
    }

    // Clickable state
    &--clickable {
        cursor: pointer;
        transition: opacity 0.3s ease;

        &:hover {
            opacity: 0.9;
        }
    }

    &__inner {
        width: 100%;
    }

    &__container {
        max-width: 1920px;
        margin: 0 auto;
        padding: 0 15px;

        @media only screen and (min-width: 1161px) {
            padding: 0 25px;
        }
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    &__link {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: inherit;
        width: 100%;

        &:hover,
        &:focus {
            color: inherit;
            text-decoration: none;
        }
    }

    &__message {
        flex: 1;
        margin: 0;
        font-weight: 400;

        // Handle line breaks properly
        br {
            @media only screen and (max-width: 767px) {
                display: none;
            }
        }

        @media only screen and (max-width: 767px) {
            font-size: 13px;
            line-height: 1.3;
        }
    }

    &__icon {
        margin-left: 8px;
        font-size: 12px;
        opacity: 0.8;

        @media only screen and (min-width: 768px) {
            font-size: 14px;
        }
    }

    // Natural document flow - no additional spacing needed
    + .page-wrapper {
        margin-top: 0;
    }

    // Mobile responsive adjustments
    @media only screen and (max-width: 1160px) {
        padding: 6px 0; // Minimal padding on mobile to reduce gap
        font-size: 13px;
    }

    @media only screen and (max-width: 480px) {
        padding: 5px 0; // Even tighter on small mobile
        font-size: 12px;

        &__container {
            padding: 0 10px;
        }

        &__message {
            font-size: 12px;
            line-height: 1.2;
        }
    }
}

// Body class adjustments when message bar is active
body.has-holiday-message-bar {
    // Simple approach: only adjust sticky navigation behavior
    .masthead {
        &.is-fixed {
            // Desktop sticky navigation
            @media only screen and (min-width: 1161px) {
                top: 40px !important; // Account for message bar height
            }

            // Mobile sticky navigation
            @media only screen and (max-width: 1160px) {
                top: 30px !important; // Account for message bar height on mobile
            }
        }
    }
}
