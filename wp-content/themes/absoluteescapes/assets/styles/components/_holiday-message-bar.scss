/**
 * Holiday Message Bar Component Styles
 */

:root {
    --holiday-message-bar-height: 50px;
    --holiday-message-bar-height-mobile: 40px;
}

.holiday-message-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 9999; // Ensure it's above everything
    width: 100%;
    padding: 12px 0;
    font-size: 14px;
    line-height: 1.4;
    
    @media only screen and (min-width: 768px) {
        padding: 15px 0;
        font-size: 16px;
    }

    // Background color variations
    &--bg-teal {
        background-color: $teal;
    }

    &--bg-blue {
        background-color: $bluegrey;
    }

    &--bg-green {
        background-color: $success;
    }

    &--bg-orange {
        background-color: #fd7e14;
    }

    &--bg-red {
        background-color: $fail;
    }

    &--bg-grey {
        background-color: $midlightgrey;
    }

    // Text color variations
    &--text-white {
        color: $white;
    }

    &--text-black {
        color: $black;
    }

    &--text-dark {
        color: $bluegrey;
    }

    // Clickable state
    &--clickable {
        cursor: pointer;
        transition: opacity 0.3s ease;

        &:hover {
            opacity: 0.9;
        }
    }

    &__inner {
        width: 100%;
    }

    &__container {
        max-width: 1920px;
        margin: 0 auto;
        padding: 0 15px;

        @media only screen and (min-width: 1161px) {
            padding: 0 25px;
        }
    }

    &__content {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    &__link {
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        color: inherit;
        width: 100%;

        &:hover,
        &:focus {
            color: inherit;
            text-decoration: none;
        }
    }

    &__message {
        flex: 1;
        margin: 0;
        font-weight: 400;

        // Handle line breaks properly
        br {
            @media only screen and (max-width: 767px) {
                display: none;
            }
        }

        @media only screen and (max-width: 767px) {
            font-size: 13px;
            line-height: 1.3;
        }
    }

    &__icon {
        margin-left: 8px;
        font-size: 12px;
        opacity: 0.8;

        @media only screen and (min-width: 768px) {
            font-size: 14px;
        }
    }

    // Push down the page wrapper to account for fixed message bar
    + .page-wrapper {
        // No margin needed here since masthead positioning handles the spacing
        margin-top: 0;
    }

    // Mobile responsive adjustments
    @media only screen and (max-width: 1160px) {
        padding: 6px 0; // Minimal padding on mobile to reduce gap
        font-size: 13px;
    }

    @media only screen and (max-width: 480px) {
        padding: 5px 0; // Even tighter on small mobile
        font-size: 12px;

        &__container {
            padding: 0 10px;
        }

        &__message {
            font-size: 12px;
            line-height: 1.2;
        }
    }
}

// Body class adjustments when message bar is active
body.has-holiday-message-bar {
    .masthead {
        // Desktop positioning adjustments
        @media only screen and (min-width: 1161px) {
            top: 50px !important; // Push down by message bar height

            &.is-fixed {
                top: 50px !important; // Maintain offset when fixed

                // Adjust the transform animations to account for message bar
                &.is-visible {
                    transform: translateY(0) !important; // Show at the correct position
                }

                &.is-hidden {
                    transform: translateY(calc(-100% - 100px)) !important; // Hide accounting for message bar
                }
            }
        }

        // Mobile positioning adjustments
        @media only screen and (max-width: 1160px) {
            top: 35px !important; // Tighter spacing on mobile
        }
    }
}
