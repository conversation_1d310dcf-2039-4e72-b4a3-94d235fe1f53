
.subnav {
    position: sticky;
    top: 0;
    z-index: 99;
    width: 100%;

    @media only screen and (max-width: 1160px) {
        top: 125px;
    }

    @media only screen and (max-width: 480px) {
        top: 115px;
    }

    .select-wrapper {
        display: none;
        width: 100%;

        @media only screen and (max-width: 1360px) {
            display: block;
        }

        &:after {
            border-color: $white;
        }
    }

    #subNav {
        padding-left: 0;
        background: transparent;
        color: $white;
    }

    + .holiday-blocks-wrapper .holiday-blocks-wrapper__container .holiday-blocks-wrapper__row .holiday-blocks-wrapper__col section {

        &:first-child {
            padding-top: 50px;
        }
    }

    &__inner {
        position: relative;
        padding: 24px 0 65px;
        background: url(../img/subnav.png) center top no-repeat;
        background-size: 100% 100%;

        @media only screen and (max-width: map-get($grid-breakpoints-max, xl)) {
            padding: 10px 0;
            background: $bluegrey;
        }

    }

    &__label {
        font-family: $headinglightsfontfamily;
        font-size: 1.9rem;
        font-weight: 500;
        color: $white;

        @media only screen and (max-width: 1460px) {
            font-size: 1.7rem;
        }
    }
    
    &__items {
        @media only screen and (max-width: 1360px) {
            display: none;
        }
    }

    &__item {
        display: inline-block;
        position: relative;
        vertical-align: middle;
        margin: 0 20px;

        &:first-child {
            margin-left: 0;
        }

        &.active {
            
            &:before {
                content: '';
                display: inline-block;
                position: absolute;
                top: 0;
                bottom: 0;
                left: -10px;
                width: 16px;
                height: 11px;
                margin: auto 0;
                background: url(../img/arrow.svg) center no-repeat;
                background-size: contain;
                transform: translateX(-100%);
            }
            a {
                color: $teal;

               
            }
        }

        a {
            font-size: 1.9rem;
            font-family: $headingfontfamily;
            font-weight: 300;
            text-decoration: none;
            color: $white;

            &:hover, &:focus {
                color: $teal;
            }
        }
    }

}